#!/usr/bin/env python3
"""
MediaMarkt Complete Scraper - Main Script
Combines listings scraper + product details scraper into one workflow
"""

import json
import time
import os
from datetime import datetime
from listings_scraper import get_product_listings, MEDIAMARKT_DOMAINS
from product_details_scraper import get_product_details

print("🛒 MediaMarkt Complete Scraper")
print("=" * 50)

def get_user_input():
    """Get user input for country and search query"""
    print("\n🌍 Available Countries:")
    countries = list(MEDIAMARKT_DOMAINS.keys())
    for i, country in enumerate(countries, 1):
        domain = MEDIAMARKT_DOMAINS[country]["domain"]
        print(f"  {i:2d}. {country} ({domain})")
    
    # Get country selection
    while True:
        try:
            choice = input(f"\nSelect country (1-{len(countries)}): ").strip()
            country_index = int(choice) - 1
            if 0 <= country_index < len(countries):
                selected_country = countries[country_index]
                break
            else:
                print(f"❌ Please enter a number between 1 and {len(countries)}")
        except ValueError:
            print("❌ Please enter a valid number")
    
    # Get search query
    default_query = "tv oled"
    query = input(f"\n🔍 Enter search query (default: '{default_query}'): ").strip()
    if not query:
        query = default_query
    
    return selected_country, query

def scrape_listings(country, query):
    """Scrape product listings for the selected country and query"""
    print(f"\n🔍 STEP 1: Scraping Product Listings")
    print("-" * 40)
    print(f"Country: {country}")
    print(f"Query: '{query}'")
    
    domain_info = MEDIAMARKT_DOMAINS[country]
    search_url = domain_info["search_url"].format(query=query.replace(" ", "%20"))
    
    print(f"\n📡 Scraping {country}...")
    listings = get_product_listings(search_url, country, domain_info)
    
    if listings:
        print(f"✅ Found {len(listings)} products")
        return listings
    else:
        print("❌ No products found")
        return []

def scrape_all_product_details(listings, query, country):
    """Extract detailed information for all products"""
    print(f"\n📦 STEP 2: Extracting Product Details")
    print("-" * 40)
    
    if not listings:
        print("❌ No product listings to process")
        return []
    
    print(f"🎯 Processing {len(listings)} products for detailed information...")
    
    detailed_products = []
    successful_count = 0
    failed_count = 0
    
    for i, product in enumerate(listings):
        print(f"\n📄 Product {i+1}/{len(listings)}")
        
        product_url = product.get("url")
        if not product_url:
            print("   ❌ No URL found, skipping...")
            failed_count += 1
            continue
        
        product_name = product.get("name", "Unknown Product")
        print(f"   🏷️ {product_name[:60]}{'...' if len(product_name) > 60 else ''}")
        
        # Get detailed product information
        detailed_product = get_product_details(product_url, product)
        
        if detailed_product:
            detailed_products.append(detailed_product)
            successful_count += 1
            
            # Show key info
            listing_info = detailed_product.get("listing_info", {})
            detailed_info = detailed_product.get("detailed_info", {})
            
            print(f"   ✅ Success - Price: {listing_info.get('price', 'N/A')} {listing_info.get('currency', '')}")
            if detailed_info:
                print(f"   📊 Detailed fields: {len(detailed_info.keys())}")
        else:
            print(f"   ❌ Failed to extract details")
            failed_count += 1
        
        # Save progress every 5 products to avoid losing data
        if (i + 1) % 5 == 0:
            temp_filename = f"temp_progress_{query.replace(' ', '_')}.json"
            with open(temp_filename, "w", encoding="utf-8") as f:
                json.dump(detailed_products, f, indent=2, ensure_ascii=False)
            print(f"   💾 Progress saved ({successful_count} products)")
        
        # Rate limiting - wait between requests
        if i < len(listings) - 1:  # Don't wait after the last request
            time.sleep(3)
    
    # Clean up temporary file
    temp_filename = f"temp_progress_{query.replace(' ', '_')}.json"
    if os.path.exists(temp_filename):
        os.remove(temp_filename)
    
    print(f"\n📊 EXTRACTION SUMMARY:")
    print(f"  ✅ Successful: {successful_count}")
    print(f"  ❌ Failed: {failed_count}")
    print(f"  📈 Success Rate: {(successful_count/len(listings)*100):.1f}%")
    
    return detailed_products

def save_listings_only(listings, query, country):
    """Save listings data when product details extraction fails"""
    print(f"\n💾 STEP 3: Saving Listings Data")
    print("-" * 40)
    
    # Create listings-only data structure
    listings_data = {
        "scraping_info": {
            "query": query,
            "country": country,
            "domain": MEDIAMARKT_DOMAINS[country]["domain"],
            "total_products_found": len(listings),
            "scraped_at": datetime.now().isoformat(),
            "scraper_version": "1.0",
            "data_type": "listings_only",
            "note": "Product details extraction failed - saving listings data only"
        },
        "products": listings,
        "summary": {
            "product_count": len(listings),
            "countries_covered": [country],
            "has_detailed_info": 0,
            "price_range": get_price_range_from_listings(listings)
        }
    }
    
    # Generate filename
    safe_query = query.replace(" ", "_").replace("/", "_").replace("\\", "_")
    safe_country = country.replace("🇪🇸 ", "").replace("🇩🇪 ", "").replace("🇳🇱 ", "").replace("🇨🇭 ", "").replace("🇵🇱 ", "").replace("🇮🇹 ", "").replace("🇦🇹 ", "").replace("🇧🇪 ", "").replace("🇭🇺 ", "").replace("🇱🇺 ", "").replace("🇵🇹 ", "").replace(" ", "_")
    filename = f"mediamarkt_listings_{safe_country}_{safe_query}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Save data
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(listings_data, f, indent=2, ensure_ascii=False)
    
    file_size = os.path.getsize(filename) / (1024 * 1024)  # Size in MB
    
    print(f"✅ Listings data saved successfully!")
    print(f"📁 File: {filename}")
    print(f"📊 Size: {file_size:.2f} MB")
    print(f"🏷️ Products: {len(listings)}")
    print(f"💡 Contains: Names, prices, URLs, images - ready for manual analysis")
    
    return filename

def get_price_range_from_listings(listings):
    """Calculate price range from listing data"""
    prices = []
    currencies = set()
    
    for product in listings:
        price = product.get("price")
        currency = product.get("currency", "EUR")
        
        if price:
            try:
                price_float = float(price)
                prices.append(price_float)
                currencies.add(currency)
            except:
                pass
    
    if prices:
        return {
            "min_price": min(prices),
            "max_price": max(prices),
            "avg_price": sum(prices) / len(prices),
            "currencies": list(currencies)
        }
    else:
        return {"min_price": None, "max_price": None, "avg_price": None, "currencies": []}

def save_comprehensive_data(detailed_products, query, country):
    """Save all data to comprehensive JSON file"""
    print(f"\n💾 STEP 3: Saving Comprehensive Data")
    print("-" * 40)
    
    # Create comprehensive data structure
    comprehensive_data = {
        "scraping_info": {
            "query": query,
            "country": country,
            "domain": MEDIAMARKT_DOMAINS[country]["domain"],
            "total_products_found": len(detailed_products),
            "scraped_at": datetime.now().isoformat(),
            "scraper_version": "1.0"
        },
        "products": detailed_products,
        "summary": {
            "product_count": len(detailed_products),
            "countries_covered": [country],
            "has_detailed_info": len([p for p in detailed_products if p.get("detailed_info")]),
            "price_range": get_price_range(detailed_products)
        }
    }
    
    # Generate filename
    safe_query = query.replace(" ", "_").replace("/", "_").replace("\\", "_")
    safe_country = country.replace("🇪🇸 ", "").replace("🇩🇪 ", "").replace("🇳🇱 ", "").replace("🇨🇭 ", "").replace("🇵🇱 ", "").replace("🇮🇹 ", "").replace("🇦🇹 ", "").replace("🇧🇪 ", "").replace("🇭🇺 ", "").replace("🇱🇺 ", "").replace("🇵🇹 ", "").replace(" ", "_")
    filename = f"mediamarkt_complete_{safe_country}_{safe_query}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    # Save data
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(comprehensive_data, f, indent=2, ensure_ascii=False)
    
    file_size = os.path.getsize(filename) / (1024 * 1024)  # Size in MB
    
    print(f"✅ Data saved successfully!")
    print(f"📁 File: {filename}")
    print(f"📊 Size: {file_size:.2f} MB")
    print(f"🏷️ Products: {len(detailed_products)}")
    
    return filename

def get_price_range(detailed_products):
    """Calculate price range from products"""
    prices = []
    currencies = set()
    
    for product in detailed_products:
        listing_info = product.get("listing_info", {})
        price = listing_info.get("price")
        currency = listing_info.get("currency", "EUR")
        
        if price:
            try:
                price_float = float(price)
                prices.append(price_float)
                currencies.add(currency)
            except:
                pass
    
    if prices:
        return {
            "min_price": min(prices),
            "max_price": max(prices),
            "avg_price": sum(prices) / len(prices),
            "currencies": list(currencies)
        }
    else:
        return {"min_price": None, "max_price": None, "avg_price": None, "currencies": []}

def show_listings_summary(filename, listings, query, country):
    """Show final summary when only listings data is available"""
    print(f"\n" + "=" * 50)
    print("📋 LISTINGS DATA SAVED SUCCESSFULLY!")
    print("=" * 50)
    
    print(f"\n📊 SESSION SUMMARY:")
    print(f"  🌍 Country: {country}")
    print(f"  🔍 Query: '{query}'")
    print(f"  📦 Products Found: {len(listings)}")
    print(f"  💾 File Created: {filename}")
    print(f"  ⚠️ Note: Product details extraction failed (422 errors)")
    
    if listings:
        # Show sample products
        print(f"\n🏷️ SAMPLE PRODUCTS FOUND:")
        for i, product in enumerate(listings[:3]):
            name = product.get("name", "Unknown")
            price = product.get("price", "N/A")
            currency = product.get("currency", "")
            print(f"  {i+1}. {name[:50]}{'...' if len(name) > 50 else ''}")
            print(f"     💰 {price} {currency}")
            print(f"     🔗 {product.get('url', 'No URL')[:60]}...")
        
        if len(listings) > 3:
            print(f"  ... and {len(listings) - 3} more products")
        
        # Show price analysis
        price_range = get_price_range_from_listings(listings)
        if price_range["min_price"]:
            print(f"\n💰 PRICE ANALYSIS:")
            print(f"  📉 Lowest: {price_range['min_price']:.2f} {price_range['currencies'][0] if price_range['currencies'] else 'EUR'}")
            print(f"  📈 Highest: {price_range['max_price']:.2f} {price_range['currencies'][0] if price_range['currencies'] else 'EUR'}")
            print(f"  📊 Average: {price_range['avg_price']:.2f} {price_range['currencies'][0] if price_range['currencies'] else 'EUR'}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    print(f"  • Try Spain (🇪🇸) or Germany (🇩🇪) for better product details extraction")
    print(f"  • Use this listings data for price monitoring and product discovery")
    print(f"  • The file contains full product URLs for manual analysis")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"  • Open {filename} to view all product data")
    print(f"  • Try a different country for detailed extraction")
    print(f"  • Use listings data for competitive analysis")

def show_final_summary(filename, detailed_products, query, country):
    """Show final summary of the scraping session"""
    print(f"\n" + "=" * 50)
    print("🎉 SCRAPING COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    
    print(f"\n📋 SESSION SUMMARY:")
    print(f"  🌍 Country: {country}")
    print(f"  🔍 Query: '{query}'")
    print(f"  📦 Products Scraped: {len(detailed_products)}")
    print(f"  💾 File Created: {filename}")
    
    if detailed_products:
        # Show sample products
        print(f"\n🏷️ SAMPLE PRODUCTS:")
        for i, product in enumerate(detailed_products[:3]):
            listing_info = product.get("listing_info", {})
            name = listing_info.get("name", "Unknown")
            price = listing_info.get("price", "N/A")
            currency = listing_info.get("currency", "")
            print(f"  {i+1}. {name[:50]}{'...' if len(name) > 50 else ''}")
            print(f"     💰 {price} {currency}")
        
        if len(detailed_products) > 3:
            print(f"  ... and {len(detailed_products) - 3} more products")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"  • Open {filename} to view all product data")
    print(f"  • Run the script again with different countries/queries")
    print(f"  • Use the data for price analysis, monitoring, etc.")

def main():
    """Main function"""
    print("🚀 Starting MediaMarkt Complete Scraper...")
    
    try:
        # Step 1: Get user input
        country, query = get_user_input()
        
        # Step 2: Scrape product listings
        listings = scrape_listings(country, query)
        
        if not listings:
            print("\n❌ No listings found. Please try a different query or country.")
            return
        
        # Step 3: Extract detailed product information
        detailed_products = scrape_all_product_details(listings, query, country)
        
        # Step 4: Save comprehensive data (even if some/all details failed)
        if detailed_products:
            filename = save_comprehensive_data(detailed_products, query, country)
        else:
            print("\n⚠️ No detailed product information extracted.")
            print("💡 Saving listings data only...")
            filename = save_listings_only(listings, query, country)
        
        # Step 5: Show final summary
        if detailed_products:
            show_final_summary(filename, detailed_products, query, country)
        else:
            show_listings_summary(filename, listings, query, country)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Scraping interrupted by user")
        print("💡 Any progress has been saved to temporary files")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")
        print("💡 Check your internet connection and API key")

if __name__ == "__main__":
    main() 