import requests
import json
import time
from urllib.parse import urljoin

print("🛒 MediaMarkt Listings Scraper")
print("=" * 50)

# API Configuration
API_KEY = "54a018dfd57d4a50bfc8792da44c122a"
API_URL = "https://api.zyte.com/v1/extract"

# MediaMarkt domains and their search URLs
MEDIAMARKT_DOMAINS = {
    "🇪🇸 Spain": {
        "domain": "mediamarkt.es",
        "search_url": "https://www.mediamarkt.es/es/search.html?query={query}",
        "base_url": "https://www.mediamarkt.es"
    },
    "🇩🇪 Germany": {
        "domain": "mediamarkt.de", 
        "search_url": "https://www.mediamarkt.de/de/search.html?query={query}",
        "base_url": "https://www.mediamarkt.de"
    },
    "🇳🇱 Netherlands": {
        "domain": "mediamarkt.nl",
        "search_url": "https://www.mediamarkt.nl/nl/search.html?query={query}",
        "base_url": "https://www.mediamarkt.nl"
    },
    "🇨🇭 Switzerland": {
        "domain": "mediamarkt.ch",
        "search_url": "https://www.mediamarkt.ch/de/search.html?query={query}",
        "base_url": "https://www.mediamarkt.ch"
    },
    "🇵🇱 Poland": {
        "domain": "mediamarkt.pl",
        "search_url": "https://mediamarkt.pl/pl/search.html?query={query}",
        "base_url": "https://mediamarkt.pl"
    },
    "🇮🇹 Italy": {
        "domain": "mediaworld.it",
        "search_url": "https://www.mediaworld.it/it/search.html?query={query}",
        "base_url": "https://www.mediaworld.it"
    },
    "🇦🇹 Austria": {
        "domain": "mediamarkt.at",
        "search_url": "https://www.mediamarkt.at/de/search.html?query={query}",
        "base_url": "https://www.mediamarkt.at"
    },
    "🇧🇪 Belgium": {
        "domain": "mediamarkt.be",
        "search_url": "https://www.mediamarkt.be/nl/search.html?query={query}",
        "base_url": "https://www.mediamarkt.be"
    },
    "🇭🇺 Hungary": {
        "domain": "mediamarkt.hu",
        "search_url": "https://www.mediamarkt.hu/hu/search.html?query={query}",
        "base_url": "https://www.mediamarkt.hu"
    },
    "🇱🇺 Luxembourg": {
        "domain": "mediamarkt.lu",
        "search_url": "https://mediamarkt.lu/pages/search?q={query}",
        "base_url": "https://mediamarkt.lu"
    },
    "🇵🇹 Portugal": {
        "domain": "mediamarkt.pt",
        "search_url": "https://mediamarkt.pt/pages/search-results-page?q={query}",
        "base_url": "https://mediamarkt.pt"
    }
}

def get_product_listings(search_url, country_name, domain_info):
    """Extract product listings from MediaMarkt search page"""
    print(f"🔍 Scraping {country_name} ({domain_info['domain']})...")
    
    try:
        api_response = requests.post(
            API_URL,
            auth=(API_KEY, ""),
            json={
                "url": search_url,
                "browserHtml": True,
                "productList": True,
                "productListOptions": {"extractFrom": "browserHtml"},
            },
        )
        
        if api_response.status_code == 200:
            response_data = api_response.json()
            
            # Save browser HTML for debugging
            if "browserHtml" in response_data:
                html_filename = f"html_{domain_info['domain'].replace('.', '_')}.html"
                with open(html_filename, "w", encoding="utf-8") as fp:
                    fp.write(response_data["browserHtml"])
                print(f"  💾 HTML saved to {html_filename}")
            
            # Extract product list
            if "productList" in response_data:
                product_list_data = response_data["productList"]
                
                # The productList is a dict with a 'products' array
                if isinstance(product_list_data, dict) and "products" in product_list_data:
                    product_list = product_list_data["products"]
                    print(f"  ✅ Found {len(product_list)} products")
                    
                    # Process product URLs to make them absolute
                    processed_products = []
                    for product in product_list:
                        # Ensure product is a dictionary
                        if isinstance(product, dict):
                            product_dict = product.copy()
                        elif isinstance(product, str):
                            # If it's a string (URL), create a dict
                            product_dict = {"url": product, "name": "Unknown"}
                        else:
                            # Try to convert to dict if possible
                            try:
                                product_dict = dict(product)
                            except:
                                product_dict = {"url": str(product), "name": "Unknown"}
                        
                        # Process URL to make it absolute
                        if "url" in product_dict and product_dict["url"]:
                            if not product_dict["url"].startswith("http"):
                                product_dict["url"] = urljoin(domain_info["base_url"], product_dict["url"])
                        
                        # Add metadata
                        product_dict["country"] = country_name
                        product_dict["domain"] = domain_info["domain"]
                        product_dict["search_url"] = search_url
                        
                        processed_products.append(product_dict)
                    
                    return processed_products
                else:
                    print(f"  ❌ Invalid productList structure")
                    return []
            else:
                print(f"  ❌ No product list found in response")
                return []
                
        else:
            print(f"  ❌ API Error: {api_response.status_code}")
            return []
            
    except Exception as e:
        print(f"  ❌ Error scraping {country_name}: {e}")
        return []

def main():
    """Main function to scrape all MediaMarkt domains"""
    print("🚀 Starting MediaMarkt listings scraper...")
    
    # Get user input for search query
    default_query = "tv oled"
    query = input(f"Enter search query (default: '{default_query}'): ").strip()
    if not query:
        query = default_query
    
    print(f"🔍 Searching for: '{query}'")
    
    # Ask user which countries to scrape
    print("\nAvailable countries:")
    countries = list(MEDIAMARKT_DOMAINS.keys())
    for i, country in enumerate(countries, 1):
        print(f"{i}. {country}")
    
    choice = input(f"\nEnter country numbers (1-{len(countries)}, comma-separated) or 'all' for all countries: ").strip()
    
    if choice.lower() == 'all':
        selected_countries = countries
    else:
        try:
            indices = [int(x.strip()) - 1 for x in choice.split(',')]
            selected_countries = [countries[i] for i in indices if 0 <= i < len(countries)]
        except:
            print("Invalid input, using all countries...")
            selected_countries = countries
    
    print(f"\n📍 Selected countries: {', '.join(selected_countries)}")
    
    # Scrape listings from selected countries
    all_listings = {}
    total_products = 0
    
    for country in selected_countries:
        domain_info = MEDIAMARKT_DOMAINS[country]
        search_url = domain_info["search_url"].format(query=query.replace(" ", "%20"))
        
        listings = get_product_listings(search_url, country, domain_info)
        
        if listings:
            all_listings[country] = {
                "domain": domain_info["domain"],
                "search_url": search_url,
                "products": listings,
                "product_count": len(listings)
            }
            total_products += len(listings)
        
        # Small delay to avoid rate limiting
        time.sleep(2)
    
    # Save all listings to JSON
    listings_filename = f"mediamarkt_listings_{query.replace(' ', '_')}.json"
    with open(listings_filename, "w", encoding="utf-8") as f:
        json.dump(all_listings, f, indent=2, ensure_ascii=False)
    
    # Create product URLs list for the details scraper
    all_product_urls = []
    for country_data in all_listings.values():
        for product in country_data["products"]:
            if "url" in product and product["url"]:
                all_product_urls.append({
                    "url": product["url"],
                    "name": product.get("name", "Unknown"),
                    "price": product.get("price", "Unknown"), 
                    "country": product.get("country", "Unknown"),
                    "domain": product.get("domain", "Unknown")
                })
    
    urls_filename = f"product_urls_{query.replace(' ', '_')}.json"
    with open(urls_filename, "w", encoding="utf-8") as f:
        json.dump(all_product_urls, f, indent=2, ensure_ascii=False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 SCRAPING SUMMARY")
    print("=" * 50)
    
    for country, data in all_listings.items():
        print(f"{country}: {data['product_count']} products")
    
    print(f"\n📈 TOTALS:")
    print(f"  Countries scraped: {len(all_listings)}")
    print(f"  Total products: {total_products}")
    
    print(f"\n💾 FILES CREATED:")
    print(f"  📁 {listings_filename} - All product listings")
    print(f"  📁 {urls_filename} - Product URLs for details scraper")
    
    print(f"\n✨ Listings scraping completed!")
    print(f"🔗 Next: Use 'product_details_scraper.py' with {urls_filename}")

if __name__ == "__main__":
    main() 