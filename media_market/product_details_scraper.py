import requests
import json
import time
import os
from datetime import datetime
from base64 import b64decode

print("📦 MediaMarkt Product Details Scraper")
print("=" * 50)

# API Configuration
API_KEY = "54a018dfd57d4a50bfc8792da44c122a"
API_URL = "https://api.zyte.com/v1/extract"

def get_product_details(product_url, product_info=None):
    """Extract detailed product information from MediaMarkt product page"""
    print(f"📄 Getting details for: {product_info.get('name', 'Unknown') if product_info else 'Product'}")
    print(f"    🔗 URL: {product_url}")
    
    try:
        api_response = requests.post(
            API_URL,
            auth=(API_KEY, ""),
            json={
                "url": product_url,
                "httpResponseBody": True,
                "product": True,
                "productOptions": {"extractFrom": "httpResponseBody", "ai": True},
                "followRedirect": True,
            },
        )
        
        if api_response.status_code == 200:
            response_data = api_response.json()
            
            # Optional: Save HTTP response body for debugging (uncomment if needed)
            # if "httpResponseBody" in response_data:
            #     http_response_body = b64decode(response_data["httpResponseBody"])
            #     debug_filename = f"debug_response_{product_info.get('domain', 'unknown')}.html"
            #     with open(debug_filename, "wb") as fp:
            #         fp.write(http_response_body)
            
            if "product" in response_data:
                product_details = response_data["product"]
                
                # Combine with original listing info if available
                if product_info:
                    combined_data = {
                        "listing_info": product_info,
                        "detailed_info": product_details,
                        "url": product_url,
                        "scraped_at": datetime.now().isoformat(),
                        "country": product_info.get("country", "Unknown"),
                        "domain": product_info.get("domain", "Unknown")
                    }
                else:
                    combined_data = {
                        "detailed_info": product_details,
                        "url": product_url,
                        "scraped_at": datetime.now().isoformat()
                    }
                
                print(f"    ✅ Details extracted successfully")
                return combined_data
            else:
                print(f"    ❌ No product details found in response")
                return None
                
        else:
            print(f"    ❌ API Error: {api_response.status_code}")
            if api_response.status_code == 429:
                print(f"    ⏳ Rate limited, waiting longer...")
                time.sleep(30)
            return None
            
    except Exception as e:
        print(f"    ❌ Error extracting details: {e}")
        return None

def load_product_urls(filename):
    """Load product URLs from JSON file"""
    try:
        with open(filename, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ File {filename} not found!")
        return None
    except Exception as e:
        print(f"❌ Error loading {filename}: {e}")
        return None

def save_progress(data, filename):
    """Save progress to avoid losing data"""
    with open(filename, "w", encoding="utf-8") as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def main():
    """Main function to scrape product details"""
    print("🚀 Starting MediaMarkt product details scraper...")
    
    # Look for product URLs files
    url_files = [f for f in os.listdir('.') if f.startswith('product_urls_') and f.endswith('.json')]
    
    if not url_files:
        print("❌ No product URLs files found!")
        print("💡 Run 'listings_scraper.py' first to generate product URLs")
        return
    
    # Let user choose which URLs file to use
    if len(url_files) == 1:
        urls_file = url_files[0]
        print(f"📁 Using URLs file: {urls_file}")
    else:
        print("\nAvailable URLs files:")
        for i, file in enumerate(url_files, 1):
            print(f"{i}. {file}")
        
        choice = input(f"Select file (1-{len(url_files)}): ").strip()
        try:
            urls_file = url_files[int(choice) - 1]
        except:
            urls_file = url_files[0]
            print(f"Invalid choice, using: {urls_file}")
    
    # Load product URLs
    product_urls = load_product_urls(urls_file)
    if not product_urls:
        return
    
    print(f"📊 Found {len(product_urls)} products to scrape")
    
    # Ask user how many products to scrape
    max_products = len(product_urls)
    limit_input = input(f"How many products to scrape? (max {max_products}, press Enter for all): ").strip()
    
    if limit_input and limit_input.isdigit():
        limit = min(int(limit_input), max_products)
    else:
        limit = max_products
    
    print(f"🎯 Scraping details for {limit} products...")
    
    # Prepare output files
    query = urls_file.replace('product_urls_', '').replace('.json', '')
    details_filename = f"product_details_{query}.json"
    combined_filename = f"combined_data_{query}.json"
    
    # Track progress
    all_product_details = []
    successful_scrapes = 0
    failed_scrapes = 0
    
    # Scrape product details
    for i, product_info in enumerate(product_urls[:limit]):
        print(f"\n📦 Progress: {i+1}/{limit}")
        
        product_url = product_info.get("url")
        if not product_url:
            print("    ❌ No URL found, skipping...")
            failed_scrapes += 1
            continue
        
        # Get detailed product information
        detailed_product = get_product_details(product_url, product_info)
        
        if detailed_product:
            all_product_details.append(detailed_product)
            successful_scrapes += 1
            
            # Save progress every 10 products
            if (i + 1) % 10 == 0:
                save_progress(all_product_details, f"temp_{details_filename}")
                print(f"    💾 Progress saved ({len(all_product_details)} products)")
        else:
            failed_scrapes += 1
        
        # Rate limiting - wait between requests
        if i < limit - 1:  # Don't wait after the last request
            time.sleep(3)
    
    # Save final results
    print(f"\n💾 Saving final results...")
    
    # Save detailed product data
    with open(details_filename, "w", encoding="utf-8") as f:
        json.dump(all_product_details, f, indent=2, ensure_ascii=False)
    
    # Create combined data with original listings
    combined_data = {
        "scraping_info": {
            "total_products_attempted": limit,
            "successful_scrapes": successful_scrapes,
            "failed_scrapes": failed_scrapes,
            "success_rate": f"{(successful_scrapes/limit)*100:.1f}%",
            "scraped_at": datetime.now().isoformat(),
            "query": query
        },
        "products_with_details": all_product_details
    }
    
    with open(combined_filename, "w", encoding="utf-8") as f:
        json.dump(combined_data, f, indent=2, ensure_ascii=False)
    
    # Clean up temporary file
    temp_file = f"temp_{details_filename}"
    if os.path.exists(temp_file):
        os.remove(temp_file)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 PRODUCT DETAILS SCRAPING SUMMARY")
    print("=" * 50)
    
    print(f"🎯 Products attempted: {limit}")
    print(f"✅ Successful scrapes: {successful_scrapes}")
    print(f"❌ Failed scrapes: {failed_scrapes}")
    print(f"📈 Success rate: {(successful_scrapes/limit)*100:.1f}%")
    
    if all_product_details:
        # Show sample product details
        sample_product = all_product_details[0]
        print(f"\n📦 Sample Product Details:")
        listing_info = sample_product.get("listing_info", {})
        detailed_info = sample_product.get("detailed_info", {})
        
        print(f"  🏷️ Name: {listing_info.get('name', 'N/A')}")
        print(f"  💰 Price: {listing_info.get('price', detailed_info.get('price', 'N/A'))}")
        print(f"  🌍 Country: {sample_product.get('country', 'N/A')}")
        print(f"  🏪 Domain: {sample_product.get('domain', 'N/A')}")
        
        if detailed_info:
            print(f"  📄 Detailed fields available: {len(detailed_info.keys())}")
            # Show some key fields if available
            key_fields = ['brand', 'model', 'description', 'features', 'specifications']
            for field in key_fields:
                if field in detailed_info and detailed_info[field]:
                    print(f"    • {field}: ✅")
    
    print(f"\n💾 FILES CREATED:")
    print(f"  📁 {details_filename} - Product details only")
    print(f"  📁 {combined_filename} - Combined listings + details data")
    
    print(f"\n✨ Product details scraping completed!")
    print(f"🎉 You now have comprehensive product data from MediaMarkt!")

def scrape_single_url():
    """Function to scrape a single product URL for testing"""
    print("\n🧪 Single Product URL Test Mode")
    print("=" * 40)
    
    url = input("Enter product URL: ").strip()
    if not url:
        print("❌ No URL provided")
        return
    
    print(f"🔍 Testing single product URL...")
    details = get_product_details(url)
    
    if details:
        test_filename = "single_product_test.json"
        with open(test_filename, "w", encoding="utf-8") as f:
            json.dump(details, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Test successful!")
        print(f"💾 Details saved to {test_filename}")
    else:
        print("❌ Test failed")

if __name__ == "__main__":
    mode = input("Choose mode: (1) Scrape from URLs file (2) Test single URL: ").strip()
    
    if mode == "2":
        scrape_single_url()
    else:
        main() 