from fastapi import FastAP<PERSON>, Query, HTTPException
from fastapi.responses import JSONResponse
from typing import List, Optional, Dict, Any
import asyncio
import json
import os
import time
import logging
from datetime import datetime

# Import the existing Media Market scraper modules
from listings_scraper import get_product_listings, MEDIAMARKT_DOMAINS
from product_details_scraper import get_product_details

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="MediaMarkt Product Scraper API",
    description="Search MediaMarkt products across multiple countries and get detailed product information.",
    version="1.0.0",
)


@app.on_event("startup")
async def on_startup():
    # Ensure results directory exists
    os.makedirs("results", exist_ok=True)
    logger.info("MediaMarkt API started successfully")


def _generate_api_filename(query: str, countries: List[str], endpoint_type: str) -> str:
    """Generate a filename for API JSON results."""
    timestamp = int(time.time())
    # Clean query for filename
    clean_query = "".join(c for c in query if c.isalnum() or c in (' ', '-', '_')).strip()
    clean_query = clean_query.replace(' ', '_')[:20]  # Limit length
    
    # Create country list for filename
    if len(countries) <= 3:
        countries_str = "_".join([c.replace("🇪🇸 ", "").replace("🇩🇪 ", "").replace("🇳🇱 ", "").replace("🇨🇭 ", "").replace("🇵🇱 ", "").replace("🇮🇹 ", "").replace("🇦🇹 ", "").replace("🇧🇪 ", "").replace("🇭🇺 ", "").replace("🇱🇺 ", "").replace("🇵🇹 ", "").replace(" ", "_") for c in countries])
    else:
        countries_str = f"{len(countries)}countries"
    
    return f"results/mediamarkt_api_{clean_query}_{countries_str}_{endpoint_type}_{timestamp}.json"


@app.get("/", tags=["meta"], summary="Tool manual")
async def manual():
    return {
        "name": "MediaMarkt Product Scraper API",
        "endpoints": {
            "/search": {
                "method": "GET",
                "params": [
                    "query – required (e.g., 'tv oled', 'laptop', 'smartphone')",
                    "countries – optional comma list or 'all' (default all)",
                    "limit – optional integer (1-50), max products per country (default 10)",
                    "include_details – optional boolean, fetch detailed product info (default false)",
                    "save_to_file – optional boolean, save results to JSON file (default false)",
                    "zyte_api_key – required",
                ],
            },
            "/listings": {
                "method": "GET",
                "params": [
                    "query – required (e.g., 'tv oled', 'laptop', 'smartphone')",
                    "countries – optional comma list or 'all' (default all)",
                    "limit – optional integer (1-50), max products per country (default 10)",
                    "save_to_file – optional boolean, save results to JSON file (default false)",
                    "zyte_api_key – required",
                ],
            },
            "/details": {
                "method": "GET",
                "params": [
                    "product_url – required, MediaMarkt product URL",
                    "save_to_file – optional boolean, save results to JSON file (default false)",
                    "zyte_api_key – required",
                ],
            }
        },
        "available_countries": list(MEDIAMARKT_DOMAINS.keys()),
    }


@app.get("/search", tags=["search"], summary="Search MediaMarkt products with optional details")
async def search(
    query: str = Query(..., min_length=1, description="Product search query (e.g., 'tv oled')"),
    countries: str = Query("all", description="Comma separated list of country names or 'all'"),
    limit: int = Query(10, ge=1, le=50, description="Max products per country"),
    include_details: bool = Query(False, description="Fetch detailed product information"),
    save_to_file: bool = Query(False, description="Save results to JSON file"),
    zyte_api_key: str = Query(..., description="Your Zyte Extract API key"),
):
    # Resolve country list
    if countries.lower() == "all":
        country_list = list(MEDIAMARKT_DOMAINS.keys())
    else:
        country_list = [c.strip() for c in countries.split(",") if c.strip()]
        # Validate country names
        valid_countries = []
        invalid_countries = []
        for country in country_list:
            if country in MEDIAMARKT_DOMAINS:
                valid_countries.append(country)
            else:
                # Try to find partial match
                found = False
                for domain_country in MEDIAMARKT_DOMAINS.keys():
                    if country.lower() in domain_country.lower():
                        valid_countries.append(domain_country)
                        found = True
                        break
                if not found:
                    invalid_countries.append(country)
        
        if invalid_countries:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": f"Invalid country names: {', '.join(invalid_countries)}",
                    "available_countries": list(MEDIAMARKT_DOMAINS.keys())
                }
            )
        country_list = valid_countries

    # Update API key in the scraper (monkey patch)
    import listings_scraper
    import product_details_scraper
    listings_scraper.API_KEY = zyte_api_key
    product_details_scraper.API_KEY = zyte_api_key

    logger.info(f"Starting MediaMarkt search for: '{query}' in countries: {country_list[:3]}{'...' if len(country_list) > 3 else ''}")
    
    try:
        all_listings = []
        search_urls = {}
        total_products = 0
        
        # Scrape listings from selected countries
        for country in country_list:
            domain_info = MEDIAMARKT_DOMAINS[country]
            search_url = domain_info["search_url"].format(query=query.replace(" ", "%20"))
            search_urls[country] = search_url
            
            # Get listings
            listings = get_product_listings(search_url, country, domain_info)
            
            if listings:
                # Limit results per country
                limited_listings = listings[:limit]
                all_listings.extend(limited_listings)
                total_products += len(limited_listings)
                
                logger.info(f"Found {len(limited_listings)} products in {country}")
            
            # Rate limiting
            time.sleep(1)
        
        if not all_listings:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "No products found for the given query",
                    "query": query,
                    "countries": country_list
                }
            )
        
        # Get detailed information if requested
        detailed_products = []
        if include_details:
            logger.info(f"Fetching detailed information for {len(all_listings)} products...")
            for product in all_listings:
                if 'url' in product and product['url']:
                    details = get_product_details(product['url'])
                    if details:
                        # Merge basic info with details
                        merged_product = {**product, **details}
                        detailed_products.append(merged_product)
                    else:
                        detailed_products.append(product)
                else:
                    detailed_products.append(product)
                # Rate limiting
                time.sleep(0.5)
        else:
            detailed_products = all_listings
        
        # Prepare response
        response_data = {
            "success": True,
            "query": query,
            "countries": country_list,
            "search_urls": search_urls,
            "timestamp": datetime.now().isoformat(),
            "total_products": len(detailed_products),
            "include_details": include_details,
            "products": detailed_products
        }
        
        # Save to file if requested
        if save_to_file:
            filename = _generate_api_filename(query, country_list, "search")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            response_data["saved_to"] = filename
            logger.info(f"Results saved to {filename}")
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Error during MediaMarkt search: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "query": query,
                "countries": country_list
            }
        )


@app.get("/listings", tags=["listings"], summary="Get MediaMarkt product listings only")
async def listings(
    query: str = Query(..., min_length=1, description="Product search query (e.g., 'tv oled')"),
    countries: str = Query("all", description="Comma separated list of country names or 'all'"),
    limit: int = Query(10, ge=1, le=50, description="Max products per country"),
    save_to_file: bool = Query(False, description="Save results to JSON file"),
    zyte_api_key: str = Query(..., description="Your Zyte Extract API key"),
):
    # Resolve country list
    if countries.lower() == "all":
        country_list = list(MEDIAMARKT_DOMAINS.keys())
    else:
        country_list = [c.strip() for c in countries.split(",") if c.strip()]
        # Validate country names
        valid_countries = []
        invalid_countries = []
        for country in country_list:
            if country in MEDIAMARKT_DOMAINS:
                valid_countries.append(country)
            else:
                # Try to find partial match
                found = False
                for domain_country in MEDIAMARKT_DOMAINS.keys():
                    if country.lower() in domain_country.lower():
                        valid_countries.append(domain_country)
                        found = True
                        break
                if not found:
                    invalid_countries.append(country)
        
        if invalid_countries:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "error": f"Invalid country names: {', '.join(invalid_countries)}",
                    "available_countries": list(MEDIAMARKT_DOMAINS.keys())
                }
            )
        country_list = valid_countries

    # Update API key in the scraper (monkey patch)
    import listings_scraper
    listings_scraper.API_KEY = zyte_api_key

    logger.info(f"Starting MediaMarkt listings search for: '{query}' in countries: {country_list[:3]}{'...' if len(country_list) > 3 else ''}")
    
    try:
        all_listings = []
        search_urls = {}
        total_products = 0
        
        # Scrape listings from selected countries
        for country in country_list:
            domain_info = MEDIAMARKT_DOMAINS[country]
            search_url = domain_info["search_url"].format(query=query.replace(" ", "%20"))
            search_urls[country] = search_url
            
            # Get listings
            listings = get_product_listings(search_url, country, domain_info)
            
            if listings:
                # Limit results per country
                limited_listings = listings[:limit]
                all_listings.extend(limited_listings)
                total_products += len(limited_listings)
                
                logger.info(f"Found {len(limited_listings)} products in {country}")
            
            # Rate limiting
            time.sleep(1)
        
        if not all_listings:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "No products found for the given query",
                    "query": query,
                    "countries": country_list
                }
            )
        
        # Prepare response
        response_data = {
            "success": True,
            "query": query,
            "countries": country_list,
            "search_urls": search_urls,
            "timestamp": datetime.now().isoformat(),
            "total_products": len(all_listings),
            "products": all_listings
        }
        
        # Save to file if requested
        if save_to_file:
            filename = _generate_api_filename(query, country_list, "listings")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            response_data["saved_to"] = filename
            logger.info(f"Results saved to {filename}")
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Error during MediaMarkt listings search: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "query": query,
                "countries": country_list
            }
        )


@app.get("/details", tags=["details"], summary="Get detailed product information by URL")
async def details(
    product_url: str = Query(..., description="MediaMarkt product URL"),
    save_to_file: bool = Query(False, description="Save results to JSON file"),
    zyte_api_key: str = Query(..., description="Your Zyte Extract API key"),
):
    # Update API key in the scraper (monkey patch)
    import product_details_scraper
    product_details_scraper.API_KEY = zyte_api_key

    logger.info(f"Fetching details for URL: {product_url}")
    
    try:
        # Get product details
        product_details = get_product_details(product_url)
        
        if not product_details:
            return JSONResponse(
                status_code=404,
                content={
                    "success": False,
                    "error": "Product details not found",
                    "product_url": product_url
                }
            )
        
        # Prepare response
        response_data = {
            "success": True,
            "product_url": product_url,
            "timestamp": datetime.now().isoformat(),
            "product_details": product_details
        }
        
        # Save to file if requested
        if save_to_file:
            # Extract product name for filename
            product_name = product_details.get('name', 'unknown')
            clean_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_')).strip()[:20]
            clean_name = clean_name.replace(' ', '_')
            filename = f"results/mediamarkt_details_{clean_name}_{int(time.time())}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(response_data, f, indent=2, ensure_ascii=False)
            response_data["saved_to"] = filename
            logger.info(f"Details saved to {filename}")
        
        return JSONResponse(content=response_data)
        
    except Exception as e:
        logger.error(f"Error fetching product details: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"Internal server error: {str(e)}",
                "product_url": product_url
            }
        )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=9308)