FROM python:3.11-slim

WORKDIR /app

# Copy requirements and install dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dependencies for FastAPI
RUN pip install fastapi uvicorn

# Copy the application code
COPY . .

# Create results directory
RUN mkdir -p results

# Expose the port
EXPOSE 9308

# Run the FastAPI application
CMD ["uvicorn", "api:app", "--host", "0.0.0.0", "--port", "9308"]